package com.mzj.py.mservice.shop.controller;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.commons.exception.CustomException;
import com.mzj.py.mservice.common.ApiBaseController;
import com.mzj.py.mservice.common.PageResult;
import com.mzj.py.mservice.shop.entity.Shop;
import com.mzj.py.mservice.shop.service.StoreService;
import com.mzj.py.mservice.shop.vo.*;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 门店管理
 * 
 * <AUTHOR>
 * @date: 2025/3/20
 * @description:
 */
@RestController
@RequestMapping("/mini/store")
public class ShopApiController extends ApiBaseController {

    @Resource
    private StoreService storeService;

    /**
     * 分页查询门店列表
     */
    @PostMapping("list")
    public ResultBean<PageResult<StorePageVo>> pageList(@RequestHeader String accessToken,
            @RequestBody StorePageParams pageParams) {
        pageParams.setStoreId(super.getShopId(accessToken));
        return storeService.pageList(pageParams);
    }

    /**
     * 新增
     */
    @PostMapping("add")
    public ResultBean<String> add(@RequestBody StoreAddVo addVo, @RequestHeader String accessToken)
            throws CustomException {
        TokenRedisVo user = super.getUser(accessToken);
        addVo.setCUserId(user.getId());
        return storeService.add(addVo);
    }

    /**
     * 修改
     * 
     * @param addVo
     * @param accessToken
     * @return
     * @throws CustomException
     */
    @PostMapping("update")
    public ResultBean<String> update(@RequestBody StoreAddVo addVo, @RequestHeader String accessToken)
            throws CustomException {
        TokenRedisVo user = super.getUser(accessToken);
        addVo.setCUserId(user.getId());
        return storeService.update(addVo);
    }

    /**
     * 删除
     * 
     * @param id
     * @param accessToken
     * @return
     * @throws CustomException
     */
    // @DeleteMapping("update/{id}")
    // public ResultBean<String> delete(@PathVariable("id") Long id, @RequestHeader
    // String accessToken) throws CustomException {
    // TokenRedisVo user = super.getUser(accessToken);
    // return storeService.delete(id);
    // }

    /**
     * 门店绑定设备
     * 
     * @param accessToken
     * @return
     */
    @PostMapping("bindDevice")
    public ResultBean<String> bindDevice(@RequestBody StoreBindDeviceParams bindParams,
            @RequestHeader String accessToken) {
        TokenRedisVo user = super.getUser(accessToken);
        bindParams.setUserId(user.getId());
        return storeService.bindDevice(bindParams);
    }

    /**
     * 根据ID获取门店详细信息
     *
     * @return
     */
    @GetMapping("detail/{id}")
    public ResultBean<StorePageVo> detail(@PathVariable("id") Long id, @RequestHeader String accessToken) {
        TokenRedisVo user = super.getUser(accessToken);
        return storeService.detail(id, user.getId());
    }

    /**
     * 获取门店关联的用户列表
     * 
     * @return
     * @throws CustomException
     */
    @PostMapping("users/{storeId}")
    public ResultBean<PageResult<StoreUserPageVo>> users(@PathVariable("storeId") Long storeId,
            @RequestBody StorePageParams pageParams) {
        return storeService.getUsers(storeId, pageParams);
    }

    /**
     * 用户绑定到门店
     * 
     * @return
     * @throws CustomException
     */
    @PostMapping("bind")
    public ResultBean<String> bindUser(@RequestBody StoreBindUserParams bindUserParams,
            @RequestHeader String accessToken) {
        TokenRedisVo user = super.getUser(accessToken);
        return storeService.bindUser(bindUserParams, user.getId());
    }

    /**
     * 用户解绑定
     * 
     * @return
     * @throws CustomException
     */
    @PostMapping("unbind")
    public ResultBean<PageResult<StoreUserPageVo>> unbindUser(@RequestBody StoreBindUserParams bindUserParams,
            @RequestHeader String accessToken) {
        return storeService.unbindUser(bindUserParams);
    }

    /**
     * 根据手机号查询总店
     */
    @PostMapping("/getParentShopByPhone")
    public ResultBean<PageResult<Shop>> getParentShopByPhone(@RequestBody StoreParentPageParams storeParentPageParams,
            @RequestHeader String accessToken) {
        return storeService.getParentShopByPhone(storeParentPageParams);
    }

    /**
     * 查询可选店员列表
     * 
     * @return
     */
    @GetMapping("/shopUserList/{shopId}")
    public ResultBean shopUserList(@PathVariable("shopId") Long shopId) {
        return ResultBean.successfulResult(storeService.shopUserList(shopId));
    }

}
