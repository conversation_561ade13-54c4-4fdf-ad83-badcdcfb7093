package com.mzj.py.mservice.home.repository;

import com.mzj.py.mservice.home.entity.Device;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Optional;

public interface DeviceRepository extends JpaRepository<Device, Long>, JpaSpecificationExecutor<Device> {
    List<Device> findBySnInAndDelStatus(List<String> snList, Integer delStatus);

    Device findBySn(String deviceSn);
}
