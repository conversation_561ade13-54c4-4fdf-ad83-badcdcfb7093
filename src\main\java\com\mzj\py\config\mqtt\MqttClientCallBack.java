package com.mzj.py.config.mqtt;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;

@Slf4j
@Component
public class MqttClientCallBack implements MqttCallback {

    @Value("${mqtt.clientId}")
    private String clientId;

    @Autowired
    private MqttClientConfig mqttClientConfig;

    /**
     * 与服务器断开的回调
     */
    @Override
    public void connectionLost(Throwable cause) {
        log.error(clientId + "client 与服务器断开连接！！异常类型: {}, 异常消息: {}",
                cause.getClass().getSimpleName(), cause.getMessage());
        log.error("连接断开堆栈信息: ", cause);
        log.info("连接断开，等待 SDK 自动重连，无需手动创建新连接");
    }

    @Override
    public void messageArrived(String topic, MqttMessage message) throws Exception {
        log.info(String.format("client 接收消息主题 : %s", topic));
        log.info(String.format("client 接收消息Qos : %d", message.getQos()));
        log.info(String.format("client 接收消息内容 : %s", new String(message.getPayload(), StandardCharsets.UTF_8)));
        log.info(String.format("client 接收消息retained : %b", message.isRetained()));
    }

    /**
     * 消息发布成功的回调
     */
    @Override
    public void deliveryComplete(IMqttDeliveryToken token) {
        IMqttAsyncClient client = token.getClient();
        String topic = Arrays.toString(token.getTopics());

        try {
            String message = JSONObject.toJSONString(token.getMessage());
            log.info("发布消息成功！ clientId: {}, topic : {}, Message: {}", client.getClientId(), topic, message);
        } catch (MqttException e) {
            throw new RuntimeException(e);
        }

    }
}
